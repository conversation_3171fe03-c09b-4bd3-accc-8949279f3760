/**
 *
 *
 */
package com.birdeye.social.businessCore;

import com.birdeye.social.dto.*;
import com.birdeye.social.dto.businessProfile.AccountListFilter;
import com.birdeye.social.dto.businessProfile.BusinessProfileDTO;
import com.birdeye.social.dto.businessProfile.BusinessUserLocationResponse;
import com.birdeye.social.external.request.business.BusinessAccountLocationRequest;
import com.birdeye.social.external.request.business.BusinessLiteRequest;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public interface IBusinessCoreService {

    Boolean isWebChatEnabled(Integer businessId);

    Boolean isWebChatEnabledByNumber(Long enterpriseId);

	BusinessLiteDTO getBusinessLite(BusinessLiteRequest businessLiteRequest);

	BusinessLiteUserDTO getEnterpriseUsers(Integer enterpriseId);

    BusinessLiteUserDTO getEnterpriseUsersWithLocation(Integer enterpriseId,List<Integer> businessIds);

    List<LocationDetailsDTO> getBusinessLocations(Integer businessId);

    List<BusinessLiteDTO> getBusinessLiteListByEnterpriseId(Integer businessId);

    BusinessDomainDTO getBusinessDomain(Integer businessId);

    BusinessOptionsDTO getBusinessOptions(Integer businessId);

    Integer getBusinessId(Long businessNumber);

    BusinessLiteDTO getBusinessLiteByNumber(Long businessNumber);

    BusinessLiteDTO getBusinessLiteByNumber(Long businessNumber, boolean locationRequired);

    String getBusinessTimezone(Integer businessId);

    BusinessHours getBusinessHours(Integer businessId);

    BusinessCoreUser getUserInfo(Integer userId);

    BusinessCoreUsersBulk getUserInfoInBulk(BusinessCoreUserBulkRequest request);
    
    Map<Integer, BusinessCoreUser> getBusinessUserForUserId(List<Integer> userIds);

    String getFullUsername(Integer userId);
    
    String getFullUsername(BusinessCoreUser user);

	BusinessLiteDTO getBusinessLite(Integer businessId, Boolean locationRequired);

	Map<String, BusinessLocationLiteDTOForGMB> getBusinessesInBulkByBusinessNumber(List<Long> businessNumbers, boolean publicUrl);

    Map<String, Object> getBusinessesInBulkByBusinessIds(List<Integer> businessIds,boolean locationRequired);


    List<Integer> getBusinessHierarchyList(Integer businessId);

    BusinessLiteDTO getBusinessLiteWithUpdated(Integer businessId);

    CustomHierarchyToLocMapping getCustomHierarchyToLocationMapping(Integer accountId, String levelAlias);

    BusinessProfileDTO getProfileData(Long businessId);

    List<BusinessBizLiteDto> getBusinessLiteDtoByBusinessIds(List<Integer> businessIds);

    BusinessCategoriesDTO getBusinessCategories(Integer businessId);

    void uploadMediaToCDN(CDNUploadDTO uploadDTO);

    BusinessLinksInfo getBusinessLinksById(Integer businessId);

    BusinessSocialEnabled getEnabledProperty(Long businessNumber);

    String getWebsiteDomain(Integer businessId);
    
    BusinessSocialNotificationDTO getSocialNotificationUsersList(Integer businessId);

    List<BusinessCoreUser> getUserDetails(Collection<Long> userIds);
    BusinessLiteUserDTO getUserDetailsWithRole(Integer enterpriseId);

    Collection<Integer> getAllowedBusinessIdsForUser(Integer accountId,Integer userId);

    void clearBusinessCache(Integer businessId, Boolean locationRequired);

    void clearBusinessCache(Long businessNumber, Boolean locationRequired);

    BusinessAccountLocationResponse getAllLocationsDetailsUnderAccount(Integer accountId, Integer userId, BusinessAccountLocationRequest request);

    BusinessCustomFieldDto getCustomFieldsTokenData(Integer id);

	List<Long> getBusinessSubHierarchyList(Long businessNumber, GetBusinessHierarchyList businessHierarchyList);

    BusinessUserLocationResponse getUserLocationsDetailsAccounts(Integer accountId, AccountListFilter request);
    BusinessProfileResponse getBusinessProfile(Integer accountId);
    BusinessIndustryResponse getBusinessIndustry(Integer accountId);
}
